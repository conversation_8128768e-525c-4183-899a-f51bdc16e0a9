package http

import (
	"log/slog"
	"net/http"
	"strconv"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// CreateTransaction godoc
//
//		@Summary		Create Transaction
//		@Description	Create a new transaction for course purchase
//	 @Security       BearerAuth
//	 @Param			item	body	models.TransactionForCreate	true	"transaction details"
//		@Tags			transactions
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.Transaction
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/transactions [post]
func (h *Handlers) CreateTransaction(ctx *gin.Context) {
	start := time.Now()
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	transactionInput := new(models.TransactionForCreate)
	if err := ctx.ShouldBindJSON(transactionInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate input
	if len(transactionInput.CourseIDs) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "At least one course ID is required"})
		return
	}

	// Get student ID from user ID
	studentID, err := h.db.GetStudentIDByUserID(ctx.Request.Context(), userID)
	if err != nil {
		slog.Error("Failed to get student ID for transaction",
			"user_id", userID,
			"error", err.Error(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve student information"})
		return
	}

	// Create transaction
	transaction := &models.Transaction{
		StudentID:     studentID,
		Status:        models.TransactionStatusPending,
		PaymentMethod: transactionInput.PaymentMethod,
	}

	createdTransaction, err := h.db.CreateTransaction(ctx.Request.Context(), transaction, transactionInput.CourseIDs)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Failed to create transaction",
			"student_id", studentID,
			"course_ids", transactionInput.CourseIDs,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("Transaction created successfully",
		"transaction_id", createdTransaction.ID,
		"student_id", studentID,
		"amount", createdTransaction.Amount,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, createdTransaction)
}

// GetTransactions godoc
//
//		@Summary		Get Transactions
//		@Description	Get transaction history for the logged-in student
//	 @Security       BearerAuth
//		@Tags			transactions
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.TransactionHistory
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/transactions [get]
func (h *Handlers) GetTransactions(ctx *gin.Context) {
	start := time.Now()
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get student ID from user ID
	studentID, err := h.db.GetStudentIDByUserID(ctx.Request.Context(), userID)
	if err != nil {
		slog.Error("Failed to get student ID for transaction history",
			"user_id", userID,
			"error", err.Error(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve student information"})
		return
	}

	transactions, err := h.db.GetStudentTransactions(ctx.Request.Context(), studentID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student transactions",
			"student_id", studentID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response format
	var transactionSummaries []models.TransactionSummary
	var totalAmount int

	for _, transaction := range transactions {
		var courses []models.CourseWithPurchased
		for _, course := range transaction.Courses {
			courses = append(courses, models.CourseWithPurchased{
				Name:           course.Name,
				Description:    course.Description,
				Price:          course.Price,
				Discount:       course.Discount,
				DurationInDays: course.DurationInDays,
				IsFree:         course.IsFree,
				CourseType:     course.CourseType,
				Purchased:      true, // Always true for transactions
			})
		}

		transactionSummaries = append(transactionSummaries, models.TransactionSummary{
			ID:               transaction.ID,
			Amount:           transaction.Amount,
			Status:           transaction.Status,
			TransactionDate:  transaction.TransactionDate,
			PaymentMethod:    transaction.PaymentMethod,
			PaymentReference: transaction.PaymentReference,
			Courses:          courses,
		})

		if transaction.Status == models.TransactionStatusCompleted {
			totalAmount += transaction.Amount
		}
	}

	response := models.TransactionHistory{
		Transactions: transactionSummaries,
		TotalAmount:  totalAmount,
	}

	duration := time.Since(start)
	slog.Info("Retrieved transaction history successfully",
		"student_id", studentID,
		"transaction_count", len(transactions),
		"total_amount", totalAmount,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, response)
}

// GetTransactionByID godoc
//
//		@Summary		Get Transaction by ID
//		@Description	Get specific transaction details by ID
//	 @Security       BearerAuth
//	 @Param			id	path	uint	true	"transaction ID"
//		@Tags			transactions
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.Transaction
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/transactions/{id} [get]
func (h *Handlers) GetTransactionByID(ctx *gin.Context) {
	start := time.Now()
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	transactionIDStr, ok := ctx.Params.Get("id")
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing transaction ID parameter"})
		return
	}

	transactionID, err := strconv.ParseUint(transactionIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid transaction ID"})
		return
	}

	// Get student ID from user ID
	studentID, err := h.db.GetStudentIDByUserID(ctx.Request.Context(), userID)
	if err != nil {
		slog.Error("Failed to get student ID for transaction retrieval",
			"user_id", userID,
			"error", err.Error(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve student information"})
		return
	}

	transaction, err := h.db.GetTransactionByID(ctx.Request.Context(), uint(transactionID))
	if err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve transaction",
			"transaction_id", transactionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Transaction not found"})
		return
	}

	// Verify that the transaction belongs to the requesting student
	if transaction.StudentID != studentID {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	duration := time.Since(start)
	slog.Info("Retrieved transaction successfully",
		"transaction_id", transactionID,
		"student_id", studentID,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, transaction)
}

// UpdateTransactionStatus godoc
//
//		@Summary		Update Transaction Status
//		@Description	Update transaction status (admin/webhook endpoint)
//	 @Security       BearerAuth
//	 @Param			id	path	uint	true	"transaction ID"
//	 @Param			item	body	models.TransactionStatusUpdate	true	"status update details"
//		@Tags			transactions
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	map[string]string
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/transactions/{id}/status [put]
func (h *Handlers) UpdateTransactionStatus(ctx *gin.Context) {
	start := time.Now()
	transactionIDStr, ok := ctx.Params.Get("id")
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing transaction ID parameter"})
		return
	}

	transactionID, err := strconv.ParseUint(transactionIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid transaction ID"})
		return
	}

	statusUpdate := new(models.TransactionStatusUpdate)
	if err := ctx.ShouldBindJSON(statusUpdate); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = h.db.UpdateTransactionStatus(ctx.Request.Context(), uint(transactionID), statusUpdate.Status, statusUpdate.PaymentReference)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Failed to update transaction status",
			"transaction_id", transactionID,
			"status", statusUpdate.Status,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("Transaction status updated successfully",
		"transaction_id", transactionID,
		"status", statusUpdate.Status,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"message": "Transaction status updated successfully"})
}
